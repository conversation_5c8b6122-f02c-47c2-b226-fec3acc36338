<template>
  <div>
    <el-dialog
      :before-close="handleClose"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      custom-class="cooperative-task-dialog"
      title="新增协同任务"
      top="3vh"
      width="900px"
    >
      <div class="cooperative-task-container">
        <el-form ref="taskForm" :model="taskForm" :rules="rules" class="form-container" label-suffix=":" label-width="160px">
          <!-- 计算中心管理组件 -->
          <computing-center-manager
            v-model="computingCenters"
            @change="handleComputingCentersChange"
          />

          <!-- 网络配置组件 -->
          <network-config
            ref="networkConfig"
            v-model="networkConfig"
            @change="handleNetworkConfigChange"
          />

          <!-- 任务流编辑组件 -->
          <task-flow-editor
            ref="taskFlowEditor"
            v-model="taskFlowConfig"
            :computing-centers="computingCenters"
            @change="handleTaskFlowChange"
          />
        </el-form>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button class="preview-btn" type="info" @click="previewYaml">预览 YAML</el-button>
        <el-button class="cancel-btn" @click="handleClose">取 消</el-button>
        <el-button class="submit-btn" type="primary" @click="submitForm">确 定</el-button>
      </span>
    </el-dialog>

    <!-- YAML预览对话框 -->
    <el-dialog
      :visible.sync="yamlPreviewVisible"
      :close-on-click-modal="false"
      append-to-body
      custom-class="task-dialog yaml-preview-dialog"
      title="YAML 预览"
      width="600px"
    >
      <div v-if="yamlFilename" class="yaml-info">
        <span>文件名: {{ yamlFilename }}</span>
      </div>
      <div v-loading="yamlLoading" class="yaml-content-container">
        <pre v-if="yamlContent" class="yaml-content">{{ yamlContent }}</pre>
        <div v-else class="yaml-empty">
          <span>暂无内容，请点击"生成YAML"按钮生成预览</span>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="cancel-btn" @click="yamlPreviewVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { generateYaml } from '@/api/yamlService'
import dialogMixin from '@/mixins/dialogMixin'
import yamlPreviewMixin from '@/mixins/yamlPreviewMixin'
import ComputingCenterManager from '@/components/common/ComputingCenterManager.vue'
import TaskFlowEditor from '@/components/common/TaskFlowEditor.vue'
import NetworkConfig from '@/components/common/NetworkConfig.vue'
import { getTaskFormRules } from '@/utils/form'
import { handleApiRequest } from '@/utils/apiHelper'
// 预览和提交都直接使用TaskFlowEditor生成的数据，不再需要yamlHelper

export default {
  name: 'CooperativeTaskDialog',
  components: {
    ComputingCenterManager,
    TaskFlowEditor,
    NetworkConfig
  },
  mixins: [
    dialogMixin,
    yamlPreviewMixin
  ],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    taskInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data () {
    return {
      taskForm: {
        // 保留一些基础字段用于表单验证
      },
      rules: {
        ...getTaskFormRules()
      },
      computingCenters: [], // 计算中心数据
      taskFlowConfig: {
        nodes: [],
        connections: []
      }, // 任务流配置数据
      networkConfig: {
        construct: false,
        accelerator: false,
        topologyTpye: 'topology01'
      }
    }
  },
  watch: {
    taskInfo: {
      handler (val) {
        if (val && Object.keys(val).length > 0) {
          // 分离网络配置、任务流配置和其他配置
          const { construct, accelerator, topologyTpye, taskFlow, ...otherConfig } = val
          this.networkConfig = { construct, accelerator, topologyTpye }
          this.taskFlowConfig = taskFlow || { nodes: [], connections: [] }
          this.taskForm = { ...this.taskForm, ...otherConfig }
        }
      },
      deep: true
    }
  },
  methods: {
    handleClose () {
      this.$refs.taskForm && this.$refs.taskForm.resetFields()
      this.dialogVisible = false
      this.computingCenters = [] // 清空计算中心列表
      this.taskFlowConfig = {
        nodes: [],
        connections: []
      } // 清空任务流配置
      this.networkConfig = {
        construct: false,
        accelerator: false,
        topologyTpye: 'topology01'
      }
      this.$emit('update:visible', false)
      this.$emit('close')
    },

    handleComputingCentersChange (centers) {
      this.computingCenters = centers
    },

    handleTaskFlowChange (config) {
      this.taskFlowConfig = config
    },

    handleNetworkConfigChange (config) {
      this.networkConfig = config
    },

    submitForm () {
      // 验证表单
      this.$refs.taskForm.validate((valid) => {
        if (!valid) {
          this.$message.error('请检查表单填写是否正确')
          return
        }

        // 验证计算中心
        if (this.computingCenters.length === 0) {
          this.$message.error('请至少添加一个计算中心')
          return
        }

        // 验证任务流配置
        if (!this.$refs.taskFlowEditor.validateTaskFlow()) {
          return
        }

        // 验证网络配置
        if (!this.$refs.networkConfig.validateNetworkConfig()) {
          return
        }

        const apiRequestData = this.prepareCooperativeTaskData()
        if (!apiRequestData) return

        handleApiRequest(() => generateYaml(apiRequestData), {
          loadingText: '正在提交协同任务...',
          successMessage: '协同任务提交成功！',
          errorMessage: '协同任务提交失败，请检查参数后重试',
          onSuccess: (response) => {
            // 延迟1秒后触发submit事件并关闭对话框
            setTimeout(() => {
              this.$emit('submit', response)
              this.handleClose() // 关闭对话框
            }, 1000)
          }
        })
      })
    },

    prepareCooperativeTaskData () {
      try {
        // 验证TaskFlowEditor组件
        if (!this.$refs.taskFlowEditor) {
          this.$message.error('任务流编辑器未初始化')
          return null
        }

        if (typeof this.$refs.taskFlowEditor.prepareTaskFlowData !== 'function') {
          this.$message.error('任务流编辑器方法不可用')
          return null
        }

        // 获取任务流基础数据（已包含nodeJson）
        const taskFlowData = this.$refs.taskFlowEditor.prepareTaskFlowData()
        if (!taskFlowData) {
          this.$message.error('任务流数据生成失败，请检查任务配置和节点设置')
          return null
        }

        // 使用协同任务对话框中选择的计算中心（优先级更高）
        if (this.computingCenters && this.computingCenters.length > 0) {
          const c2netComputingCenterList = this.computingCenters.map(center => ({
            c2netComputingCenterSpecification: {
              nodeSpecification: {
                nGBMemoriesPerGraphicsCard: center.nGBMemoriesPerGraphicsCard,
                nCpus: center.nCpus,
                nGBMemories: center.nGBMemories,
                nGraphicsCards: center.nGraphicsCards,
                graphicsCardType: center.graphicsCardType
              },
              nNodes: center.nNodes,
              pOPS: center.pOPS,
              name: center.name
            },
            nCopiedC2netComputingCenters: 1
          }))

          // 替换计算中心列表
          taskFlowData.c2netComputingCenterList = c2netComputingCenterList
        }

        // 添加网络配置
        taskFlowData.networkConfig = this.networkConfig

        // 协同任务数据包含nodeJson: !!(taskFlowData.params && taskFlowData.params.nodeJson)
        return taskFlowData
      } catch (error) {
        // 准备协同任务数据时出错: error
        return null
      }
    },

    // 重写 yamlPreviewMixin 中的 prepareTaskData 方法
    prepareTaskData () {
      // 预览和提交都使用相同的数据处理逻辑
      return this.prepareCooperativeTaskData()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/dialog.scss';

/* 特定于CooperativeTaskDialog的额外样式 */
.cooperative-task-container {
  .form-container {
    padding: 0 10px;
  }
}
</style>
